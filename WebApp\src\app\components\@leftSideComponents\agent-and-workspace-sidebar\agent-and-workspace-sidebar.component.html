<!-- Agent and Workspace Sidebar Container -->
<div
  class="h-full flex flex-col bg-gradient-to-b shadow-xl border-r"
  [ngClass]="{
    'from-[#1a1a1f] to-[#2b2b33] text-white border-[#3a3a45] shadow-black/20':
      themeService.isDarkMode(),
    'from-[#f8fafc] to-[#ffffff] text-gray-900 border-gray-200 shadow-gray-200/50':
      !themeService.isDarkMode()
  }"
>
  <!-- Accordion Content Section -->
  <div class="flex-1 flex flex-col h-full overflow-hidden">
    <!-- AGENTS SECTION (TOP) -->
    <div class="flex flex-col">
      <!-- Agents Accordion Header -->
      <div
        (click)="toggleAccordionSection('agents')"
        class="group flex-shrink-0 px-4 py-3 border-b cursor-pointer transition-all duration-300 ease-out backdrop-blur-sm"
        [ngClass]="{
          'border-[#3a3a45] hover:bg-gradient-to-r hover:from-[#2a2a35] hover:to-[#323240] hover:shadow-lg':
            themeService.isDarkMode(),
          'border-gray-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:shadow-md':
            !themeService.isDarkMode()
        }"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <!-- Modern Icon Container -->
            <div
              class="w-8 h-8 rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110"
              [ngClass]="{
                'bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg':
                  themeService.isDarkMode(),
                'bg-gradient-to-br from-blue-400 to-purple-500 shadow-md':
                  !themeService.isDarkMode()
              }"
            >
            </div>
            <div class="flex flex-col">
              <h3
                class="text-base font-semibold tracking-tight transition-colors duration-200"
                [ngClass]="{
                  'text-white group-hover:text-blue-200':
                    themeService.isDarkMode(),
                  'text-gray-900 group-hover:text-blue-700':
                    !themeService.isDarkMode()
                }"
              >
                Agents
              </h3>
              <p
                class="text-xs opacity-70"
                [ngClass]="{
                  'text-gray-300': themeService.isDarkMode(),
                  'text-gray-500': !themeService.isDarkMode()
                }"
              >
                AI assistants & tools
              </p>
            </div>
            <!-- Modern Badge -->
            <span
              class="ml-2 text-xs font-medium px-2.5 py-1 rounded-full transition-all duration-200"
              [ngClass]="{
                'bg-blue-500/20 text-blue-300 border border-blue-500/30':
                  themeService.isDarkMode(),
                'bg-blue-100 text-blue-700 border border-blue-200':
                  !themeService.isDarkMode()
              }"
            >
              {{ agents.length }}
            </span>
          </div>
          <!-- Modern Toggle Icon -->
          <div
            class="w-6 h-6 rounded-lg flex items-center justify-center transition-all duration-300 group-hover:scale-110"
            [ngClass]="{
              'bg-[#3a3a45] group-hover:bg-blue-500/20':
                themeService.isDarkMode(),
              'bg-gray-100 group-hover:bg-blue-100': !themeService.isDarkMode()
            }"
          >
            <i
              class="text-sm transition-transform duration-300"
              [ngClass]="{
                'ri-arrow-down-s-line': !isAccordionExpanded('agents'),
                'ri-arrow-up-s-line rotate-180': isAccordionExpanded('agents'),
                'text-gray-300 group-hover:text-blue-300':
                  themeService.isDarkMode(),
                'text-gray-600 group-hover:text-blue-600':
                  !themeService.isDarkMode()
              }"
            ></i>
          </div>
        </div>
      </div>

      <!-- Agents Collapsible Content -->
      <div
        *ngIf="isAccordionExpanded('agents')"
        class="overflow-hidden transition-all duration-500 ease-out bg-gradient-to-b"
        [ngClass]="{
          'from-[#1e1e24] to-[#252530]': themeService.isDarkMode(),
          'from-gray-50 to-white': !themeService.isDarkMode()
        }"
      >
        <div class="max-h-96 overflow-y-auto px-2 py-1">
          <!-- Loading State for Agents -->
          <div
            *ngIf="isLoadingAgents"
            class="flex items-center justify-center py-8"
          >
            <div class="flex flex-col items-center gap-3">
              <div
                class="w-8 h-8 border-3 border-t-transparent rounded-full animate-spin"
                [ngClass]="{
                  'border-blue-400': themeService.isDarkMode(),
                  'border-blue-500': !themeService.isDarkMode()
                }"
              ></div>
              <span
                class="text-sm font-medium"
                [ngClass]="{
                  'text-gray-300': themeService.isDarkMode(),
                  'text-gray-600': !themeService.isDarkMode()
                }"
              >
                Loading agents...
              </span>
            </div>
          </div>

          <!-- Agents List -->
          <div
            *ngIf="!isLoadingAgents && !agentsError && agents.length > 0"
            class="space-y-0"
          >
            @for (agentName of agents; track $index) {
            <div
              (click)="navigateToAgentChat(agentName)"
              class="group flex items-center gap-2 py-2 px-3 rounded-lg cursor-pointer transition-all duration-300 relative overflow-hidden"
              [ngClass]="{
                'hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]':
                  themeService.isDarkMode(),
                'hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:shadow-md hover:scale-[1.02] active:scale-[0.98]':
                  !themeService.isDarkMode()
              }"
            >
              <!-- Modern Active indicator -->
              <div
                class="absolute left-0 top-1 bottom-1 w-1 rounded-r-full transition-all duration-300 opacity-0 group-hover:opacity-100"
                [ngClass]="{
                  'bg-gradient-to-b from-blue-400 to-purple-500':
                    themeService.isDarkMode(),
                  'bg-gradient-to-b from-blue-500 to-purple-600':
                    !themeService.isDarkMode()
                }"
              ></div>

              <!-- Agent Info -->
              <div class="flex-1 min-w-0">
                <span
                  class="font-semibold text-sm transition-colors duration-300 block truncate"
                  [ngClass]="{
                    'text-gray-200 group-hover:text-white':
                      themeService.isDarkMode(),
                    'text-gray-800 group-hover:text-gray-900':
                      !themeService.isDarkMode()
                  }"
                >
                  {{ formatAgentName(agentName) }}
                </span>
                <span
                  class="text-xs opacity-70 transition-opacity duration-300 group-hover:opacity-100"
                  [ngClass]="{
                    'text-gray-400': themeService.isDarkMode(),
                    'text-gray-500': !themeService.isDarkMode()
                  }"
                >
                  AI Assistant
                </span>
              </div>

              <!-- Arrow Icon -->
              <i
                class="ri-arrow-right-s-line text-lg opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1"
                [ngClass]="{
                  'text-blue-300': themeService.isDarkMode(),
                  'text-blue-600': !themeService.isDarkMode()
                }"
              ></i>
            </div>
            }
          </div>
        </div>
      </div>
    </div>

    <!-- WORKSPACES SECTION -->
    <div
      class="flex flex-col"
      [ngClass]="{
        'mt-auto': !isAccordionExpanded('workspaces'),
        'mt-0': isAccordionExpanded('workspaces')
      }"
    >
      <!-- Workspaces Accordion Header -->
      <div
        (click)="toggleAccordionSection('workspaces')"
        class="group flex-shrink-0 px-6 py-4 border-b cursor-pointer transition-all duration-300 ease-out backdrop-blur-sm"
        [ngClass]="{
          'border-[#3a3a45] hover:bg-gradient-to-r hover:from-[#2a2a35] hover:to-[#323240] hover:shadow-lg':
            themeService.isDarkMode(),
          'border-gray-200 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-teal-50 hover:shadow-md':
            !themeService.isDarkMode()
        }"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <!-- Modern Icon Container -->
            <div
              class="w-8 h-8 rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110"
              [ngClass]="{
                'bg-gradient-to-br from-emerald-500 to-teal-600 shadow-lg':
                  themeService.isDarkMode(),
                'bg-gradient-to-br from-emerald-400 to-teal-500 shadow-md':
                  !themeService.isDarkMode()
              }"
            >
              <i class="ri-building-line text-white text-sm"></i>
            </div>
            <div class="flex flex-col">
              <h3
                class="text-base font-semibold tracking-tight transition-colors duration-200"
                [ngClass]="{
                  'text-white group-hover:text-emerald-200':
                    themeService.isDarkMode(),
                  'text-gray-900 group-hover:text-emerald-700':
                    !themeService.isDarkMode()
                }"
              >
                Workspaces
              </h3>
              <p
                class="text-xs opacity-70"
                [ngClass]="{
                  'text-gray-300': themeService.isDarkMode(),
                  'text-gray-500': !themeService.isDarkMode()
                }"
              >
                Project environments
              </p>
            </div>
            <!-- Modern Badge -->
            <span
              class="ml-2 text-xs font-medium px-2.5 py-1 rounded-full transition-all duration-200"
              [ngClass]="{
                'bg-emerald-500/20 text-emerald-300 border border-emerald-500/30':
                  themeService.isDarkMode(),
                'bg-emerald-100 text-emerald-700 border border-emerald-200':
                  !themeService.isDarkMode()
              }"
            >
              {{ workspaces.length }}
            </span>
          </div>
          <!-- Modern Toggle Icon -->
          <div
            class="w-6 h-6 rounded-lg flex items-center justify-center transition-all duration-300 group-hover:scale-110"
            [ngClass]="{
              'bg-[#3a3a45] group-hover:bg-emerald-500/20':
                themeService.isDarkMode(),
              'bg-gray-100 group-hover:bg-emerald-100':
                !themeService.isDarkMode()
            }"
          >
            <i
              class="text-sm transition-transform duration-300"
              [ngClass]="{
                'ri-arrow-down-s-line': !isAccordionExpanded('workspaces'),
                'ri-arrow-up-s-line rotate-180':
                  isAccordionExpanded('workspaces'),
                'text-gray-300 group-hover:text-emerald-300':
                  themeService.isDarkMode(),
                'text-gray-600 group-hover:text-emerald-600':
                  !themeService.isDarkMode()
              }"
            ></i>
          </div>
        </div>
      </div>

      <!-- Workspaces Collapsible Content -->
      <div
        *ngIf="isAccordionExpanded('workspaces')"
        class="flex-1 overflow-hidden transition-all duration-500 ease-out bg-gradient-to-b"
        [ngClass]="{
          'from-[#1e1e24] to-[#252530]': themeService.isDarkMode(),
          'from-gray-50 to-white': !themeService.isDarkMode()
        }"
      >
        <div class="h-full overflow-y-auto px-2 py-1">
          <!-- Loading State for Workspaces -->
          <div
            *ngIf="isLoadingWorkspaces"
            class="flex items-center justify-center py-8"
          >
            <div class="flex flex-col items-center gap-3">
              <div
                class="w-8 h-8 border-3 border-t-transparent rounded-full animate-spin"
                [ngClass]="{
                  'border-emerald-400': themeService.isDarkMode(),
                  'border-emerald-500': !themeService.isDarkMode()
                }"
              ></div>
              <span
                class="text-sm font-medium"
                [ngClass]="{
                  'text-gray-300': themeService.isDarkMode(),
                  'text-gray-600': !themeService.isDarkMode()
                }"
              >
                Loading workspaces...
              </span>
            </div>
          </div>

          <!-- Workspaces List -->
          <div
            *ngIf="
              !isLoadingWorkspaces && !workspacesError && workspaces.length > 0
            "
            class="space-y-0"
          >
            @for (workspace of workspaces; track workspace.id) {
            <div
              (click)="navigateToWorkspace(workspace)"
              class="group flex items-center gap-2 py-2 px-3 rounded-lg cursor-pointer transition-all duration-300 relative overflow-hidden"
              [ngClass]="{
                'hover:bg-gradient-to-r hover:from-emerald-500/10 hover:to-teal-500/10 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]':
                  themeService.isDarkMode(),
                'hover:bg-gradient-to-r hover:from-emerald-50 hover:to-teal-50 hover:shadow-md hover:scale-[1.02] active:scale-[0.98]':
                  !themeService.isDarkMode()
              }"
            >
              <!-- Modern Active indicator -->
              <div
                class="absolute left-0 top-1 bottom-1 w-1 rounded-r-full transition-all duration-300 opacity-0 group-hover:opacity-100"
                [ngClass]="{
                  'bg-gradient-to-b from-emerald-400 to-teal-500':
                    themeService.isDarkMode(),
                  'bg-gradient-to-b from-emerald-500 to-teal-600':
                    !themeService.isDarkMode()
                }"
              ></div>

              <!-- Workspace Info -->
              <div class="flex-1 min-w-0">
                <span
                  class="font-semibold text-sm transition-colors duration-300 block truncate"
                  [ngClass]="{
                    'text-gray-200 group-hover:text-white':
                      themeService.isDarkMode(),
                    'text-gray-800 group-hover:text-gray-900':
                      !themeService.isDarkMode()
                  }"
                >
                  {{ workspace.title }}
                </span>
                <span
                  class="text-xs opacity-70 transition-opacity duration-300 group-hover:opacity-100"
                  [ngClass]="{
                    'text-gray-400': themeService.isDarkMode(),
                    'text-gray-500': !themeService.isDarkMode()
                  }"
                >
                  Project Environment
                </span>
              </div>

              <!-- Arrow Icon -->
              <i
                class="ri-arrow-right-s-line text-lg opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1"
                [ngClass]="{
                  'text-emerald-300': themeService.isDarkMode(),
                  'text-emerald-600': !themeService.isDarkMode()
                }"
              ></i>
            </div>
            }
          </div>

          <!-- Error State for Workspaces -->
          <div
            *ngIf="workspacesError"
            class="mx-3 my-4 p-4 rounded-xl border"
            [ngClass]="{
              'bg-red-500/10 border-red-500/30 text-red-300':
                themeService.isDarkMode(),
              'bg-red-50 border-red-200 text-red-700':
                !themeService.isDarkMode()
            }"
          >
            <div class="flex items-center gap-3">
              <i class="ri-error-warning-line text-lg"></i>
              <div>
                <p class="font-medium text-sm">Error loading workspaces</p>
                <p class="text-xs opacity-80">{{ workspacesError }}</p>
              </div>
            </div>
          </div>

          <!-- Empty State for Workspaces -->
          <div
            *ngIf="
              !isLoadingWorkspaces &&
              !workspacesError &&
              workspaces.length === 0
            "
            class="flex flex-col items-center justify-center py-12 text-center"
          >
            <div
              class="w-16 h-16 rounded-2xl flex items-center justify-center mb-4"
              [ngClass]="{
                'bg-gradient-to-br from-emerald-500/20 to-teal-500/20 border border-emerald-500/30':
                  themeService.isDarkMode(),
                'bg-gradient-to-br from-emerald-100 to-teal-100 border border-emerald-200':
                  !themeService.isDarkMode()
              }"
            >
              <i
                class="ri-building-line text-2xl"
                [ngClass]="{
                  'text-emerald-300': themeService.isDarkMode(),
                  'text-emerald-600': !themeService.isDarkMode()
                }"
              ></i>
            </div>
            <h4
              class="font-semibold text-sm mb-1"
              [ngClass]="{
                'text-gray-200': themeService.isDarkMode(),
                'text-gray-800': !themeService.isDarkMode()
              }"
            >
              No workspaces available
            </h4>
            <p
              class="text-xs opacity-70"
              [ngClass]="{
                'text-gray-400': themeService.isDarkMode(),
                'text-gray-500': !themeService.isDarkMode()
              }"
            >
              Project environments will appear here
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
